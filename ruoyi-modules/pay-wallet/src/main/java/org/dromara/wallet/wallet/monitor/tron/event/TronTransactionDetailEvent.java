package org.dromara.wallet.wallet.monitor.tron.event;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.chain.model.tron.TronLogModel;
import org.dromara.common.scanning.monitor.TronMonitorEvent;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.wallet.helper.TronHttpApiHelper;
import org.dromara.wallet.wallet.monitor.tron.utils.TronEventLogUtils;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * TRON交易详情获取事件
 * 负责调用TRON API获取交易详细信息并解析事件日志
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>API调用：调用getTransactionInfoById获取交易详细信息</li>
 *   <li>日志解析：使用TronEventLogUtils解析事件日志</li>
 *   <li>数据丰富：为交易模型添加完整的事件日志信息</li>
 *   <li>错误处理：API调用失败时的降级处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TronTransactionDetailEvent implements TronMonitorEvent {

    private final TronHttpApiHelper tronHttpApiHelper;
    private final TronConfigFacade configFacade;

    /**
     * 存储解析后的事件日志的键名
     */
    public static final String PARSED_LOGS_KEY = "tron.parsedLogs";

    @Override
    public void call(TransactionModel transactionModel) {
        try {
            // 检查是否为TRON交易模型
            if (transactionModel.getTronTransactionModel() == null) {
                log.debug("{}链跳过非TRON交易模型", configFacade.getChainName());
                return;
            }

            // 获取TRON交易模型
            org.dromara.common.scanning.chain.model.tron.TronTransactionModel tronTxModel =
                transactionModel.getTronTransactionModel();

            String txId = tronTxModel.getTxID();
            if (txId == null) {
                log.debug("{}链交易ID为空，跳过详情获取", configFacade.getChainName());
                return;
            }

            // 检查地址过滤结果，避免不必要的API调用
            Boolean addressFilterPassed = TronAddressFilterEvent.getAddressFilterResult(transactionModel);
            if (addressFilterPassed != null && !addressFilterPassed) {
                log.debug("{}链交易{}未通过地址过滤检查，跳过详情获取", configFacade.getChainName(), txId);
                // 存储空列表，避免后续Event出错
                storeParsedLogs(transactionModel, new ArrayList<>());
                return;
            }

            // 获取区块号和时间戳
            BigInteger blockNumber = getBlockNumber(tronTxModel);
            Long blockTimeStamp = getBlockTimeStamp(tronTxModel);

            // 获取交易详细信息并解析事件日志
            List<TronLogModel> parsedLogs = queryTransactionLogs(txId, blockNumber, blockTimeStamp);

            // 存储解析后的日志供后续Event使用
            storeParsedLogs(transactionModel, parsedLogs);

            if (!parsedLogs.isEmpty()) {
                log.debug("{}链交易{}获取到{}个事件日志",
                    configFacade.getChainName(), txId, parsedLogs.size());
            } else {
                log.debug("{}链交易{}没有相关的事件日志", configFacade.getChainName(), txId);
            }

        } catch (Exception e) {
            log.error("{}链交易详情获取失败: {}", configFacade.getChainName(), e.getMessage());
            // 异常时存储空列表，避免后续Event出错
            storeParsedLogs(transactionModel, new ArrayList<>());
        }
    }

    /**
     * 查询交易的详细日志信息并转换为业务层模型
     */
    private List<TronLogModel> queryTransactionLogs(String txId, BigInteger blockNumber, Long blockTimeStamp) {
        try {
            // 1. 调用TRON API获取交易详细信息
            JsonNode transactionInfo = tronHttpApiHelper.getTransactionInfoById(txId);
            if (transactionInfo == null) {
                log.debug("{}链交易{}没有找到交易信息", configFacade.getChainName(), txId);
                return new ArrayList<>();
            }

            // 2. 解析事件日志
            Long actualBlockTimeStamp = blockTimeStamp;
            if (transactionInfo.has("blockTimeStamp")) {
                actualBlockTimeStamp = transactionInfo.get("blockTimeStamp").asLong();
            }

            List<TronLogModel> logs = TronEventLogUtils.parseEventLogs(
                transactionInfo, txId, blockNumber, actualBlockTimeStamp);

            if (logs.isEmpty()) {
                log.debug("{}链交易{}没有事件日志", configFacade.getChainName(), txId);
                return new ArrayList<>();
            }

            log.debug("{}链交易{}解析出{}个原始事件日志",
                configFacade.getChainName(), txId, logs.size());
            return logs;

        } catch (Exception e) {
            log.error("{}链查询交易{}日志失败: {}", configFacade.getChainName(), txId, e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取区块号
     */
    private BigInteger getBlockNumber(org.dromara.common.scanning.chain.model.tron.TronTransactionModel tronTxModel) {
        try {
            if (tronTxModel.getTronBlockHeaderModel() != null &&
                tronTxModel.getTronBlockHeaderModel().getTronRawDataModel() != null) {
                return tronTxModel.getTronBlockHeaderModel().getTronRawDataModel().getNumber();
            }
        } catch (Exception e) {
            log.debug("获取区块号失败: {}", e.getMessage());
        }
        return BigInteger.ZERO;
    }

    /**
     * 获取区块时间戳
     */
    private Long getBlockTimeStamp(org.dromara.common.scanning.chain.model.tron.TronTransactionModel tronTxModel) {
        try {
            if (tronTxModel.getTronBlockHeaderModel() != null &&
                tronTxModel.getTronBlockHeaderModel().getTronRawDataModel() != null) {
                return tronTxModel.getTronBlockHeaderModel().getTronRawDataModel().getTimestamp();
            }
        } catch (Exception e) {
            log.debug("获取区块时间戳失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 存储解析后的日志
     */
    private void storeParsedLogs(TransactionModel transactionModel, List<TronLogModel> parsedLogs) {
        // 简化：不再存储，直接在TronBusinessProcessEvent中处理
        log.trace("{}链解析后的日志数量: {}", configFacade.getChainName(), parsedLogs.size());
    }

    /**
     * 获取解析后的日志（供后续Event使用）
     */
    public static List<TronLogModel> getParsedLogs(TransactionModel transactionModel) {
        // 简化：返回空列表，让TronBusinessProcessEvent直接处理
        return new ArrayList<>();
    }
}
